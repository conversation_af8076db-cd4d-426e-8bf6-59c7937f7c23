version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: tradingagents_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-password}
      MYSQL_DATABASE: ${DB_NAME:-tradingagents_db}
      MYSQL_USER: ${DB_USER:-tradinguser}
      MYSQL_PASSWORD: ${DB_PASSWORD:-password}
    ports:
      - "3306:3306"
    volumes:
      - /home/<USER>/mysql_data:/var/lib/mysql
      - /home/<USER>/docker/mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - tradingagents_network

  redis:
    image: redis:7-alpine
    container_name: tradingagents_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - tradingagents_network

  # 개발용 phpMyAdmin (선택사항)
#   phpmyadmin:
#     image: phpmyadmin/phpmyadmin
#     container_name: tradingagents_phpmyadmin
#     restart: unless-stopped
#     environment:
#       PMA_HOST: mysql
#       PMA_PORT: 3306
#       PMA_USER: root
#       PMA_PASSWORD: ${DB_PASSWORD:-password}
#     ports:
#       - "8080:80"
#     depends_on:
#       - mysql
#     networks:
#       - tradingagents_network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  tradingagents_network:
    driver: bridge 